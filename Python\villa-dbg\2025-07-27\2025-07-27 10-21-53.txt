2025-07-27 10:21:53.619|INFO   |init_logging@98|init~
2025-07-27 10:21:54.098|INFO   |init_logging@102|caller_module_path: D:\work\xstarwalker168\Python\Finance\schwab_villa.py
2025-07-27 10:21:54.098|INFO   |init_logging@103|log file: `D:/work/xstarwalker168/Python/villa-dbg/2025-07-27/2025-07-27 10-21-53.txt`
2025-07-27 10:21:54.125|INFO   |init_logging@109|current git commit short hash: a957eae
2025-07-27 10:21:54.125|INFO   |init_logging@115|vbt.is_numba_enabled: False
2025-07-27 10:21:54.126|INFO   |main@1008|Command line arguments: ['D:\\work\\xstarwalker168\\Python\\Finance\\schwab_villa.py', 'analyse_options']
2025-07-27 10:21:56.331|WARNING|urlopen@868|Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='cg.play-analytics.com', port=443): Read timed out. (read timeout=1)")': /
2025-07-27 10:21:57.535|WARNING|urlopen@868|Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'RemoteDisconnected('Remote end closed connection without response')': /
2025-07-27 10:21:59.003|WARNING|urlopen@868|Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'RemoteDisconnected('Remote end closed connection without response')': /
2025-07-27 10:22:02.783|INFO   |load_token@42|Loading token from file D:\work\xstarwalker168\Python\Finance/config/schwab_token.json
2025-07-27 10:22:04.648|INFO   |handle@960|buying power: 1297.71
2025-07-27 10:22:06.062|INFO   |get_earning_symbols@704|market is closed for NEXT after-market-close, only for analysis~
2025-07-27 10:22:07.467|INFO   |get_earning_symbols@719| 2 candidates: ['NUE', 'CLS']
2025-07-27 10:22:12.743|INFO   |speculate_options@765|xlist_df:
  Symbol EarningDate
0    NUE  2025-07-26
1    CLS  2025-07-26
2025-07-27 10:22:12.744|INFO   |speculate_options@769|



2025-07-27 10:22:12.744|INFO   |speculate_options@772|$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$ working on `   1 / 2` ( 50.00%), `NUE   `$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$
