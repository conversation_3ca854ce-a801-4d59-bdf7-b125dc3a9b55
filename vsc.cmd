```
>	System.Private.CoreLib.dll!System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw() Line 53	C# 
 	System.Private.CoreLib.dll!System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess(System.Threading.Tasks.Task task) Line 143	C# 
 	System.Private.CoreLib.dll!System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(System.Threading.Tasks.Task task, System.Threading.Tasks.ConfigureAwaitOptions options) Line 118	C# 
 	System.Private.CoreLib.dll!System.Runtime.CompilerServices.ConfiguredTaskAwaitable<System.__Canon>.ConfiguredTaskAwaiter.GetResult() Line 515	C# 
 	[Waiting on Async Operation, double-click or press enter to view Async Call Stacks]	 
 	QuantConnect.Common.dll!QuantConnect.Extensions.SynchronouslyAwaitTaskResult<System.Net.WebSockets.WebSocketReceiveResult>(System.Threading.Tasks.Task<System.Net.WebSockets.WebSocketReceiveResult> task) Line 3328	C# 
 	QuantConnect.Common.dll!QuantConnect.Extensions.SynchronouslyAwaitTask<System.Net.WebSockets.WebSocketReceiveResult>(System.Threading.Tasks.Task<System.Net.WebSockets.WebSocketReceiveResult> task) Line 3348	C# 
 	QuantConnect.Brokerages.dll!QuantConnect.Brokerages.WebSocketClientWrapper.ReceiveMessage(System.Net.WebSockets.ClientWebSocket webSocket, System.Threading.CancellationToken ct, byte[] receiveBuffer) Line 299	C# 
 	QuantConnect.Brokerages.dll!QuantConnect.Brokerages.WebSocketClientWrapper.HandleConnection() Line 244	C# 
 	QuantConnect.Brokerages.dll!QuantConnect.Brokerages.WebSocketClientWrapper.Connect.AnonymousMethod__10_0() Line 89	C# 
 	System.Private.CoreLib.dll!System.Threading.ExecutionContext.RunInternal(System.Threading.ExecutionContext executionContext, System.Threading.ContextCallback callback, object state) Line 179	C# 
 	System.Private.CoreLib.dll!System.Threading.Tasks.Task.ExecuteWithThreadLocal(ref System.Threading.Tasks.Task currentTaskSlot, System.Threading.Thread threadPoolThread) Line 2342	C# 
```

```
System.Threading.Tasks.TaskCanceledException: 'The operation was canceled.' 
```

```
SocketException: The I/O operation has been aborted because of either a thread exit or an application request.
```

when `_tradingWebSocket?.Close();` is invoked, I will have above exceptions. 
`_client?.CloseOutputAsync(WebSocketCloseStatus.NormalClosure, "", _cts.Token).SynchronouslyAwaitTask();` was invoked, `var messageData = ReceiveMessage(_client, connectionCts.Token, receiveBuffer);` will be triggered, but current _client.State is Aborted. ReceiveMessage will have exceptions.

help me solve the issues


if you make any code changes, use the following command to test building errors:
```
      dotnet build ""D:\work\Lean.Brokerages.Binance\QuantConnect.BinanceBrokerage\QuantConnect.BinanceBrokerage.csproj"" --configuration Debug 
      dotnet build "D:\work\xstarwalker168\Python\Finance\QuantConnectLean\CryptoBot(C#)\cCryptoBot\cCryptoBot.csproj" --configuration Debug
```