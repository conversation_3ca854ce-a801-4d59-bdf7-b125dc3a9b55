2025-07-27 10:20:11.484|INFO   |init_logging@98|init~
2025-07-27 10:20:11.988|INFO   |init_logging@102|caller_module_path: D:\work\xstarwalker168\Python\Finance\schwab_villa.py
2025-07-27 10:20:11.988|INFO   |init_logging@103|log file: `D:/work/xstarwalker168/Python/villa-dbg/2025-07-27/2025-07-27 10-20-11.txt`
2025-07-27 10:20:12.021|INFO   |init_logging@109|current git commit short hash: a957eae
2025-07-27 10:20:12.022|INFO   |init_logging@115|vbt.is_numba_enabled: False
2025-07-27 10:20:12.022|INFO   |main@1008|Command line arguments: ['D:\\work\\xstarwalker168\\Python\\Finance\\schwab_villa.py', 'analyse_options']
2025-07-27 10:20:13.819|WARNING|urlopen@868|Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'RemoteDisconnected('Remote end closed connection without response')': /
2025-07-27 10:20:15.400|WARNING|urlopen@868|Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'RemoteDisconnected('Remote end closed connection without response')': /
2025-07-27 10:20:17.001|WARNING|urlopen@868|Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'RemoteDisconnected('Remote end closed connection without response')': /
2025-07-27 10:20:21.085|INFO   |client_from_manual_flow@452|Creating new token with callback URL 'https://127.0.0.1' and token path 'D:\work\xstarwalker168\Python\Finance/config/schwab_token.json'
2025-07-27 10:21:24.104|INFO   |update_token@33|Updating token to file D:\work\xstarwalker168\Python\Finance/config/schwab_token.json
